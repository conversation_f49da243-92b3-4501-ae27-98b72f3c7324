import { InfoService } from './info.service';

const cluster = require('cluster');

const { env } = process;

describe('InfoService test suite', () => {
  beforeEach(() => {
    jest.resetModules();
    process.env = { ...env };
  });

  afterEach(() => {
    process.env = env;
  });

  const infoService = new InfoService();
  test('infoService is an object', () => {
    expect(typeof infoService).toEqual('object');
  });

  test('infoService.cpuQuantity is a number', () => {
    expect(typeof infoService.cpuQuantity).toEqual('number');
  });

  test('infoService.nodeVersion is a string', () => {
    expect(typeof infoService.nodeVersion).toEqual('string');
  });

  test('infoService.appVersion is a string', () => {
    expect(typeof infoService.appVersion).toEqual('string');
  });

  test('infoService.currentMachineTime is an object', () => {
    expect(typeof infoService.currentMachineTime).toEqual('object');
  });

  test('infoService.workerStartedTime is an object', () => {
    expect(typeof infoService.workerStartedTime).toEqual('object');
  });

  test('infoService.healthCheck is a function', () => {
    expect(typeof infoService.healthCheck).toEqual('function');
  });

  test('infoService.healthCheck; should return string OK; isMaster=false', () => {
    cluster.isMaster = false;
    expect(infoService.healthCheck()).toEqual('OK');
  });

  test.skip('infoService.healthCheck; should throw an exception; liveWorkers=0; needToBe=0; isMaster=true', () => {
    cluster.isMaster = true;
    cluster.workers = {};
    expect(() => {
      infoService.healthCheck();
    }).toThrowError('HEALTH_CHECK_ERROR');
  });

  test.skip('infoService.healthCheck; should return result; liveWorkers=2; needToBe=1; isMaster=true', () => {
    const clusterWorkers = [
      {
        process: {
          pid: '890022'
        },
        id: '55446655',
        state: 'listening'
      },
      {
        process: {
          pid: '890023'
        },
        id: '55446656',
        state: 'listening'
      }
    ];

    const healthCheckResponse = [
      {
        cluster: '55446655',
        state: 'listening'
      },
      {
        cluster: '55446656',
        state: 'listening'
      }
    ];

    cluster.isMaster = true;
    cluster.workers = clusterWorkers;
    expect(infoService.healthCheck()).toEqual(JSON.stringify(healthCheckResponse));
  });
});
