SO<PERSON> aka <PERSON><PERSON> aka do<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, śled<PERSON>:

<PERSON><PERSON> repo - school_of_fish
Przepisane z bigDataHerring (python)

Kontrollery:
ad.xml: - Przyjmujemy request. - Weryfikujemy id5 i gdpr (consent). - <PERSON><PERSON><PERSON> parametry są poprawne, to generujemy/wyciagamy z bazy herringId. - Je<PERSON><PERSON> parmetry są niepoprawne, to nie modyfikujemy requestu. - Zwracamy redirect do adservera z dodanymi parametrami (Zawsze code 307).

    get_id:
        - Przyjmujemy request.
        - Weryfikujemy id5 oraz parametry wzgledem wersji.
        - Jeśli id5 jest poprawne (forma "ID5\*..."), to dekodujemy id5 i wyciągamy identityId5.
        <!-- One nam posłużą dalej do weryfikacji użytkownika. -->
        - w zależności od parametrów, szukamy w bazie spratID i herringId. Jak sie nie znaj<PERSON><PERSON> (względem przesłanych parametrów) to generujemy nowe.
        - Zwracamy response z Identyfikatorami.
