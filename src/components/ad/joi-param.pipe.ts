import { PipeTransform } from '@nestjs/common';
import type <PERSON><PERSON><PERSON> from 'joi';
import { RequestContextService } from '../../context/request-context.service';
import { logger } from '../../logger';

export class JoiParamPipe<I, O> implements PipeTransform<I, O> {
  constructor(private readonly schema: JoiNS.ObjectSchema<O>) {}

  transform(value: I): O {
    const { error, value: v } = this.schema.validate(value, {
      abortEarly: false,
      convert: true,
      stripUnknown: false
    });
    if (error) {
      this.handleError(error);
    }
    return v;
  }

  private handleError(error: JoiNS.ValidationError) {
    const errMessages = error.details.map(({ message }) => message);

    logger.error(`VALIDATION`, { errMessages });

    if (RequestContextService.isDebugMode()) {
      RequestContextService.addValidationErrors(errMessages);
    }
  }
}
