import Joi from 'joi';
import { logger } from '../../logger';
import { Id5ParamValue } from '../../types';
import { validateId5Prefix } from '../herring/herring.schema';

export interface IAdSchema extends Record<string, string | boolean> {
  id5: Id5ParamValue;
  gdpr: '0' | '1';
  isDebug: boolean;
}

export const adSchema = Joi.object<IAdSchema>({
  id5: Joi.string()
    .min(1)
    .required()
    .custom(validateId5Prefix)
    .error((errors) => {
      errors.forEach((err) => {
        switch (err.code) {
          case 'any.required':
            err.message = 'ID5_IS_REQUIRED';
            break;
          case 'string.base':
            err.message = 'ID5_MUST_BE_STRING';
            break;
          case 'string.empty':
            err.message = 'ID5_CANNOT_BE_EMPTY';
            break;
          case 'string.id5Prefix':
            err.message = 'ID5_INVALID_PREFIX';
            break;
          default:
            const message = 'ID5_UNKNOWN_ERROR';
            logger.fatal(message, err.code);
            err.message = message;
            break;
        }
      });
      return errors;
    }),
  gdpr: Joi.string()
    .valid('0', '1')
    .required()
    .error((errors) => {
      errors.forEach((err) => {
        switch (err.code) {
          case 'any.required':
            err.message = 'CONSENT_IS_REQUIRED';
            break;
          case 'string.base':
            err.message = 'CONSENT_MUST_BE_STRING';
            break;
          case 'string.empty':
            err.message = 'CONSENT_CANNOT_BE_EMPTY';
            break;
          case 'any.only':
            err.message = 'CONSENT_MUST_BE_0_OR_1';
            break;
          default:
            const message = 'CONSENT_UNKNOWN_ERROR';
            logger.fatal(message, err.code);
            err.message = message;
            break;
        }
      });
      return errors;
    }), // consent
  isDebug: Joi.boolean().default(false).optional()
}).unknown(true);
