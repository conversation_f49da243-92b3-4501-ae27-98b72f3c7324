{"name": "herring", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:gh": "jest --silent --runInBand"}, "dependencies": {"@automock/jest": "^2.1.0", "@fastify/compress": "^8.1.0", "@fastify/helmet": "^13.0.1", "@fastify/static": "^8.1.1", "@joi/date": "^2.1.1", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.11", "@nestjs/core": "^11.0.11", "@nestjs/platform-express": "^11.0.11", "@nestjs/platform-fastify": "^11.0.11", "@nestjs/schedule": "^5.0.1", "axios": "^1.8.4", "class-transformer": "^0.5.1", "compression": "^1.8.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dd-trace": "^5.52.0", "dotenv": "^16.4.7", "envalid": "^8.0.0", "fastify": "^5.2.1", "fastify-static": "^4.7.0", "fastify-swagger": "^5.2.0", "html-entities": "^2.6.0", "ioredis": "^5.6.0", "jest-junit": "^16.0.0", "joi": "^17.13.3", "joi-class-decorators": "^1.1.2", "lodash": "^4.17.21", "nestjs-joi": "^1.11.0", "node-color-log": "^12.0.1", "qs": "^6.14.0", "rxjs": "^7.8.2", "simple-xml-to-json": "^1.2.3", "xml-js": "^1.6.11"}, "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@nestjs/testing": "^11.0.11", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.13.10", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2"}, "volta": {"node": "22.15.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.(test|spec)\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=22.15.0"}}